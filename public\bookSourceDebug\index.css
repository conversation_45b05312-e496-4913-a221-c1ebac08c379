@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeInUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  min-height: 100vh;
}
.editor {
  display: flex;
  align-items: stretch;
  padding: 20px;
  gap: 20px;
  animation: fadeInUp 0.8s ease-out;
}

.setbox,
.menu,
.outbox {
  flex: 1;
  display: flex;
  flex-flow: column;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 20px;
  animation: fadeInUp 1s ease-out;
}

.setbox::-webkit-scrollbar,
.menu::-webkit-scrollbar,
.outbox::-webkit-scrollbar {
  width: 8px;
}

.setbox::-webkit-scrollbar-track,
.menu::-webkit-scrollbar-track,
.outbox::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.setbox::-webkit-scrollbar-thumb,
.menu::-webkit-scrollbar-thumb,
.outbox::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.setbox::-webkit-scrollbar-thumb:hover,
.menu::-webkit-scrollbar-thumb:hover,
.outbox::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
.menu {
  justify-content: flex-start;
  max-width: 120px;
  min-width: 120px;
  animation: slideInLeft 1s ease-out 0.2s both;
}

.menu .button {
  width: 100%;
  height: 40px;
  min-height: 40px;
  margin: 8px 0px;
  cursor: pointer;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.menu .button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu .button:active {
  transform: translateY(0px);
}
@keyframes stroker {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -240;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.button rect {
  width: 100%;
  height: 100%;
  fill: transparent;
  stroke: rgba(255, 255, 255, 0.6);
  stroke-width: 2px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.button:hover rect {
  stroke: rgba(255, 255, 255, 0.9);
  stroke-width: 3px;
}

.button rect.busy {
  stroke: #ff6b6b;
  stroke-dasharray: 30 90;
  animation: stroker 1s linear infinite, pulse 2s ease-in-out infinite;
}

.button text {
  text-anchor: middle;
  fill: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 12px;
  transition: all 0.3s ease;
}

.button:hover text {
  fill: rgba(255, 255, 255, 1);
  font-size: 13px;
  dominant-baseline: middle;
}
.setbox {
  min-width: 40em;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.rules {
  overflow: auto;
  padding: 10px 0;
}

.tabbox {
  flex: 1;
  display: flex;
  flex-flow: column;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 15px;
  margin-top: 10px;
}

.rules > * {
  display: flex;
  margin: 8px 0;
  align-items: center;
}

.rules > * > div {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  min-width: 120px;
  font-size: 14px;
}

.rules textarea {
  flex: 1;
  margin-left: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  font-size: 13px;
  transition: all 0.3s ease;
  resize: vertical;
}

.rules textarea:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.rules textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.rules > *,
.rules > * > div,
.rules textarea {
  min-height: 1.2em;
}

textarea {
  word-break: break-all;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}
.tabtitle {
  display: flex;
  z-index: 1;
  justify-content: flex-end;
  margin-bottom: 10px;
  gap: 5px;
}

.tabtitle > div {
  cursor: pointer;
  padding: 12px 20px;
  border-bottom: 3px solid transparent;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px 12px 0 0;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tabtitle > div:hover {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.tabtitle > .this {
  color: rgba(255, 255, 255, 1);
  border-bottom-color: #4ebbe4;
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}
.tabbody {
  flex: 1;
  display: flex;
  margin-top: -1px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0 0 12px 12px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  height: 0;
  overflow: hidden;
}

.tabbody > * {
  flex: 1;
  flex-flow: column;
  display: none;
  padding: 15px;
}

.tabbody > .this {
  display: flex;
}
.tabbody > * > .titlebar {
  display: flex;
  margin-bottom: 10px;
}

.tabbody > * > .titlebar > * {
  flex: 1;
  margin: 2px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  font-size: 13px;
  transition: all 0.3s ease;
}

.tabbody > * > .titlebar > *:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.tabbody > * > .context {
  flex: 1;
  flex-flow: column;
  border: 0;
  padding: 15px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.tabbody > * > .inputbox {
  border: 0;
  border-bottom: rgba(255, 255, 255, 0.3) solid 2px;
  height: 20px;
  text-align: center;
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
  padding: 5px;
  transition: all 0.3s ease;
}

.tabbody > * > .inputbox:focus {
  outline: none;
  border-bottom-color: rgba(255, 255, 255, 0.6);
}
.link > * {
  display: flex;
  margin: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 0;
  transition: all 0.3s ease;
}

.link > *:hover {
  color: rgba(255, 255, 255, 1);
  border-bottom-color: rgba(255, 255, 255, 0.5);
  transform: translateX(5px);
}

#RuleList > label > * {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  margin: 4px 0;
  cursor: pointer;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

#RuleList > label > *:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

#RuleList input[type="radio"] {
  display: none;
}

#RuleList input[type="radio"]:checked + * {
  background: linear-gradient(135deg, #15cda8, #4ebbe4);
  color: white;
  box-shadow: 0 4px 12px rgba(21, 205, 168, 0.3);
  transform: scale(1.02);
}
.isError {
  color: #ff6b6b;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.outbox {
  animation: fadeInUp 1s ease-out 0.6s both;
}

/* 添加一些特殊的动画效果 */
.setbox:hover,
.outbox:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .editor {
    flex-direction: column;
    gap: 15px;
  }

  .setbox,
  .menu,
  .outbox {
    max-height: none;
    min-height: 300px;
  }

  .menu {
    max-width: none;
    min-width: auto;
  }

  .menu .button {
    width: auto;
    min-width: 100px;
    display: inline-block;
    margin: 5px;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
