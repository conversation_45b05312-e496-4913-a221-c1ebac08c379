body {
  margin: 0;
}
.editor {
  display: flex;
  align-items: stretch;
}
.setbox,
.menu,
.outbox {
  flex: 1;
  display: flex;
  flex-flow: column;
  max-height: 100vh;
  overflow-y: auto;
}
.menu {
  justify-content: center;
  max-width: 90px;
  margin: 0 5px;
}
.menu .button {
  width: 90px;
  height: 30px;
  min-height: 30px;
  margin: 5px 0px;
  cursor: pointer;
}
@keyframes stroker {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -240;
  }
}
.button rect {
  width: 100%;
  height: 100%;
  fill: transparent;
  stroke: #666;
  stroke-width: 2px;
}
.button rect.busy {
  stroke: #fd1850;
  stroke-dasharray: 30 90;
  animation: stroker 1s linear infinite;
}
.button text {
  text-anchor: middle;
  dominant-baseline: middle;
}
.setbox {
  min-width: 40em;
}
.rules {
  overflow: auto;
}
.tabbox {
  flex: 1;
  display: flex;
  flex-flow: column;
}
.rules > * {
  display: flex;
  margin: 2px 0;
}
.rules textarea {
  flex: 1;
  margin-left: 5px;
}
.rules > *,
.rules > * > div,
.rules textarea {
  min-height: 1.2em;
}
textarea {
  word-break: break-all;
}
.tabtitle {
  display: flex;
  z-index: 1;
  justify-content: flex-end;
}
.tabtitle > div {
  cursor: pointer;
  padding: 1px 10px 0 10px;
  border-bottom: 3px solid transparent;
  font-weight: bold;
}
.tabtitle > .this {
  color: #4f9da6;
  border-bottom-color: #4ebbe4;
}
.tabbody {
  flex: 1;
  display: flex;
  margin-top: -1px;
  border: 1px solid #a9a9a9;
  height: 0;
}
.tabbody > * {
  flex: 1;
  flex-flow: column;
  display: none;
}
.tabbody > .this {
  display: flex;
}
.tabbody > * > .titlebar {
  display: flex;
}
.tabbody > * > .titlebar > * {
  flex: 1;
  margin: 1px 1px 1px 1px;
}
.tabbody > * > .context {
  flex: 1;
  flex-flow: column;
  border: 0;
  padding: 5px;
  overflow-y: auto;
}
.tabbody > * > .inputbox {
  border: 0;
  border-bottom: #a9a9a9 solid 1px;
  height: 15px;
  text-align: center;
}
.link > * {
  display: flex;
  margin: 5px;
  border-bottom: 1px solid;
  text-decoration: none;
}
#RuleList > label > * {
  background: #eee;
  padding-left: 3px;
  margin: 2px 0;
  cursor: pointer;
}
#RuleList input[type="radio"] {
  display: none;
}
#RuleList input[type="radio"]:checked + * {
  background: #15cda8;
}
.isError {
  color: #ff0000;
}
