<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Reader书源编辑器</title>
    <link rel="icon" href="../favicon.ico">
    <link rel="stylesheet" type="text/css" href="index.css"/>
</head>

<body>
<div class="editor">
    <div class="setbox">
        <div>
            <a href="../index.html">←主页</a>
            <b>书源</b>
        </div>
        <div class="rules">
            <div><b>基本</b></div>
            <div>
                <div>源域名　:</div>
                <textarea rows="1" id="bookSourceUrl" class="base" title="bookSourceUrl"
                          placeholder="<必填>通常填写网站主页,例: https://www.qidian.com"></textarea>
            </div>
            <div>
                <div>源类型　:</div>
                <textarea rows="1" id="bookSourceType" class="base" title="bookSourceType"
                          placeholder="&lt;必填&gt;0:文本 1:音频 2:图片 3:文件(只提供下载的网站)"></textarea>
            </div>
            <div>
                <div>源名称　:</div>
                <textarea rows="1" id="bookSourceName" class="base" title="bookSourceName"
                          placeholder="&lt;必填&gt;会显示在源列表"></textarea>
            </div>
            <div>
                <div>源分组　:</div>
                <textarea rows="1" id="bookSourceGroup" class="base" title="bookSourceGroup"
                          placeholder="&lt;选填&gt;描述源的特征信息"></textarea>
            </div>
            <div>
                <div>源注释　:</div>
                <textarea rows="1" id="bookSourceComment" class="base" title="bookSourceComment"
                          placeholder="&lt;选填&gt;描述源作者和状态"></textarea>
            </div>
            <div style="display: none;">
                <div>登录地址:</div>
                <textarea rows="1" id="loginUrl" class="base" title="loginUrl"
                          placeholder="&lt;选填&gt;填写网站登录网址,仅在需要登录的源有用"></textarea>
            </div>
            <div style="display: none;">
                <div>登录界面:</div>
                <textarea rows="3" id="loginUi" class="base" title="loginUi"
                          placeholder="&lt;选填&gt;自定义登录界面"></textarea>
            </div>
            <div style="display: none;">
                <div>登录检测:</div>
                <textarea rows="3" id="loginCheckJs" class="base" title="loginCheckJs"
                          placeholder="&lt;选填&gt;登录检测js"></textarea>
            </div>
            <div style="display: none;">
                <div>并发率　:</div>
                <textarea rows="1" id="concurrentRate" class="base" title="concurrentRate"
                          placeholder="&lt;选填&gt;并发率"></textarea>
            </div>
            <div>
                <div>请求头　:</div>
                <textarea rows="3" id="header" class="base" title="header"
                          placeholder="&lt;选填&gt;客户端标识"></textarea>
            </div>
            <div>
                <div>链接验证:</div>
                <textarea rows="1" id="bookUrlPattern" class="base" title="bookUrlPattern"
                          placeholder="&lt;选填&gt;当详情页URL与源URL的域名不一致时有效，用于添加网址"></textarea>
            </div>
            <p></p>
            <div><b>搜索</b></div>
            <div>
                <div>搜索地址:</div>
                <textarea rows="1" id="searchUrl" class="base" title="searchUrl"
                          placeholder="[域名可省略]/search.php@kw={{key}}"></textarea>
            </div>
            <div style="display: none">
                <div>校验文字:</div>
                <textarea rows="1" id="ruleSearch_checkKeyWord" class="ruleSearch"
                          title="checkKeyWord"
                          placeholder="校验关键字"></textarea>
            </div>
            <div>
                <div>列表规则:</div>
                <textarea rows="1" id="ruleSearch_bookList" class="ruleSearch" title="bookList"
                          placeholder="选择书籍节点 (规则结果为List&lt;Element&gt;)"></textarea>
            </div>
            <div>
                <div>书名规则:</div>
                <textarea rows="1" id="ruleSearch_name" class="ruleSearch" title="name"
                          placeholder="选择节点书名 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>作者规则:</div>
                <textarea rows="1" id="ruleSearch_author" class="ruleSearch" title="author"
                          placeholder="选择节点作者 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>分类规则:</div>
                <textarea rows="1" id="ruleSearch_kind" class="ruleSearch" title="kind"
                          placeholder="选择节点分类信息 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>字数规则:</div>
                <textarea rows="1" id="ruleSearch_wordCount" class="ruleSearch" title="wordCount"
                          placeholder="选择节点字数信息 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>最新章节:</div>
                <textarea rows="1" id="ruleSearch_lastChapter" class="ruleSearch"
                          title="lastChapter"
                          placeholder="选择节点最新章节 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>简介规则:</div>
                <textarea rows="1" id="ruleSearch_intro" class="ruleSearch" title="intro"
                          placeholder="选择节点书籍简介 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>封面规则:</div>
                <textarea rows="1" id="ruleSearch_coverUrl" class="ruleSearch" title="coverUrl"
                          placeholder="选择节点书籍封面 (规则结果为String类型的url)"></textarea>
            </div>
            <div>
                <div>详情地址:</div>
                <textarea rows="1" id="ruleSearch_bookUrl" class="ruleSearch" title="bookUrl"
                          placeholder="选择书籍详情页网址 (规则结果为String类型的url)"></textarea>
            </div>
            <p></p>
            <div><b>发现</b></div>
            <div>
                <div>发现地址:</div>
                <textarea rows="6" id="exploreUrl" class="base" title="exploreUrl"
                          placeholder="内容能显示在发现菜单&#10;每行一条发现分类(网址域名可省略)，例：&#10;名称1::网址(Url)1&#10;名称2::网址(Url)2&#10;..."></textarea>
            </div>
            <div>
                <div>列表规则:</div>
                <textarea rows="1" id="ruleExplore_bookList" class="ruleExplore" title="bookList"
                          placeholder="选择书籍节点 (规则结果为List&lt;Element&gt;)"></textarea>
            </div>
            <div>
                <div>书名规则:</div>
                <textarea rows="1" id="ruleExplore_name" class="ruleExplore" title="name"
                          placeholder="选择节点书名 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>作者规则:</div>
                <textarea rows="1" id="ruleExplore_author" class="ruleExplore" title="author"
                          placeholder="选择节点作者 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>分类规则:</div>
                <textarea rows="1" id="ruleExplore_kind" class="ruleExplore" title="kind"
                          placeholder="选择节点分类信息 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>字数规则:</div>
                <textarea rows="1" id="ruleExplore_wordCount" class="ruleExplore" title="wordCount"
                          placeholder="选择节点字数信息 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>最新章节:</div>
                <textarea rows="1" id="ruleExplore_lastChapter" class="ruleExplore"
                          title="lastChapter"
                          placeholder="选择节点最新章节 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>简介规则:</div>
                <textarea rows="1" id="ruleExplore_intro" class="ruleExplore" title="intro"
                          placeholder="选择节点书籍简介 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>封面规则:</div>
                <textarea rows="1" id="ruleExplore_coverUrl" class="ruleExplore" title="coverUrl"
                          placeholder="选择节点书籍封面 (规则结果为String类型的url)"></textarea>
            </div>
            <div>
                <div>详情地址:</div>
                <textarea rows="1" id="ruleExplore_bookUrl" class="ruleExplore" title="bookUrl"
                          placeholder="选择书籍详情页网址 (规则结果为String类型的url)"></textarea>
            </div>
            <p></p>
            <div><b>详情</b></div>
            <div>
                <div>预处理　:</div>
                <textarea rows="3" id="ruleBookInfo_init" class="ruleBookInfo" title="init"
                          placeholder="用于加速详情信息检索，只支持AllInOne规则"></textarea>
            </div>
            <div>
                <div>书名规则:</div>
                <textarea rows="1" id="ruleBookInfo_name" class="ruleBookInfo" title="name"
                          placeholder="选择节点书名 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>作者规则:</div>
                <textarea rows="1" id="ruleBookInfo_author" class="ruleBookInfo" title="author"
                          placeholder="选择节点作者 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>分类规则:</div>
                <textarea rows="1" id="ruleBookInfo_kind" class="ruleBookInfo" title="kind"
                          placeholder="选择节点分类信息 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>字数规则:</div>
                <textarea rows="1" id="ruleBookInfo_wordCount" class="ruleBookInfo"
                          title="wordCount"
                          placeholder="选择节点字数信息 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>最新章节:</div>
                <textarea rows="1" id="ruleBookInfo_lastChapter" class="ruleBookInfo"
                          title="lastChapter"
                          placeholder="选择节点最新章节 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>简介规则:</div>
                <textarea rows="1" id="ruleBookInfo_intro" class="ruleBookInfo" title="intro"
                          placeholder="选择节点书籍简介 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>封面规则:</div>
                <textarea rows="1" id="ruleBookInfo_coverUrl" class="ruleBookInfo" title="coverUrl"
                          placeholder="选择节点书籍封面 (规则结果为String类型的url)"></textarea>
            </div>
            <div>
                <div>目录地址:</div>
                <textarea rows="1" id="ruleBookInfo_tocUrl" class="ruleBookInfo" title="tocUrl"
                          placeholder="选择书籍详情页网址 (规则结果为String类型的url, 与详情页相同时可省略)"></textarea>
            </div>
            <p></p>
            <div><b>目录</b></div>
            <div>
                <div>列表规则:</div>
                <textarea rows="3" id="ruleToc_chapterList" class="ruleToc" title="chapterList"
                          placeholder="选择目录列表的章节节点 (规则结果为List&lt;Element&gt;)"></textarea>
            </div>
            <div>
                <div>章节名称:</div>
                <textarea rows="1" id="ruleToc_chapterName" class="ruleToc" title="chapterName"
                          placeholder="选择章节名称 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>章节地址:</div>
                <textarea rows="1" id="ruleToc_chapterUrl" class="ruleToc" title="chapterUrl"
                          placeholder="选择章节链接 (规则结果为String类型的Url)"></textarea>
            </div>
            <div>
                <div>卷名标识:</div>
                <textarea rows="1" id="ruleToc_isVolume" class="ruleToc" title="isVolume"
                          placeholder="章节名称是否是卷名 (规则结果为Bool)"></textarea>
            </div>
            <div>
                <div>收费标识:</div>
                <textarea rows="1" id="ruleToc_isVip" class="ruleToc" title="isVip"
                          placeholder="章节是否为VIP章节 (规则结果为Bool)"></textarea>
            </div>
            <div style="display: none;">
                <div>购买标识:</div>
                <textarea rows="1" id="ruleToc_isPay" class="ruleToc" title="isPay"
                          placeholder="章节是否为已购买 (规则结果为Bool)"></textarea>
            </div>
            <div>
                <div>章节信息:</div>
                <textarea rows="1" id="ruleToc_updateTime" class="ruleToc" title="updateTime"
                          placeholder="选择章节信息 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>翻页规则:</div>
                <textarea rows="1" id="ruleToc_nextTocUrl" class="ruleToc" title="nextTocUrl"
                          placeholder="选择目录下一页链接 (规则结果为List&lt;String&gt;或String)"></textarea>
            </div>
            <p></p>
            <div><b>正文</b></div>
            <div>
                <div>脚本注入:</div>
                <textarea rows="3" id="ruleContent_webJs" class="ruleContent" title="webJs"
                          placeholder="注入javascript，用于模拟鼠标点击等，必须有返回值，一般为String类型"></textarea>
            </div>
            <div>
                <div>正文规则:</div>
                <textarea rows="1" id="ruleContent_content" class="ruleContent" title="content"
                          placeholder="选择正文内容 (规则结果为String)"></textarea>
            </div>
            <div>
                <div>翻页规则:</div>
                <textarea rows="1" id="ruleContent_nextContentUrl" class="ruleContent"
                          title="nextContentUrl"
                          placeholder="选择下一分页(不是下一章)链接 (规则结果为String类型的Url)"></textarea>
            </div>
            <div>
                <div>资源正则:</div>
                <textarea rows="1" id="ruleContent_sourceRegex" class="ruleContent"
                          title="sourceRegex"
                          placeholder="匹配资源的url特征，用于嗅探"></textarea>
            </div>
            <div>
                <div>替换规则:</div>
                <textarea rows="1" id="ruleContent_replaceRegex" class="ruleContent"
                          title="replaceRegex"
                          placeholder="多页内容合并后替换，用于正文净化"></textarea>
            </div>
            <div>
                <div>图片样式:</div>
                <textarea rows="1" id="ruleContent_imageStyle" class="ruleContent"
                          title="imageStyle"
                          placeholder="FULL:铺满 不填:默认样式"></textarea>
            </div>
            <div style="display: none;">
                <div>购买操作:</div>
                <textarea rows="1" id="ruleContent_payAction" class="ruleContent"
                          title="payAction"
                          placeholder="购买章节 返回链接或js"></textarea>
            </div>
            <p></p>
            <div><b>其它规则</b></div>
            <div>
                <div>启用搜索:</div>
                <textarea rows="1" id="enabled" class="base" title="enabled"
                          placeholder="启用: true  关闭: false (可选,默认true)"></textarea>
            </div>
            <div>
                <div>启用发现:</div>
                <textarea rows="1" id="enabledExplore" class="base" title="enabledExplore"
                          placeholder="启用: true  关闭: false (可选,默认true)"></textarea>
            </div>
            <div style="display: none;">
                <div>搜索权重:</div>
                <textarea rows="1" id="weight" class="base" title="weight"
                          placeholder="整数: 0~N (可选,默认0) | 数字越大越靠前"></textarea>
            </div>
            <div style="display: none;">
                <div>排序编号:</div>
                <textarea rows="1" id="customOrder" class="base" title="customOrder"
                          placeholder="整数: 0~N (可选,默认0) | 数字越小越靠前"></textarea>
            </div>
            <div style="display:none;">
                <div>更新时间:</div>
                <textarea rows="1" id="lastUpdateTime" class="base" title="lastUpdateTime"
                          placeholder="毫秒级时间戳 (自动生成) | 请勿手动填写"></textarea>
            </div>
        </div>
    </div>
    <div class="menu">
        <svg class="button">
            <text x="50%" y="55%">⇈推送源</text>
            <rect id="push"></rect>
        </svg>
        <svg class="button">
            <text x="50%" y="55%">⇊拉取源</text>
            <rect id="pull"></rect>
        </svg>
        <svg class="button">
            <text x="50%" y="55%">⋘编辑源</text>
            <rect id="editor"></rect>
        </svg>
        <svg class="button">
            <text x="50%" y="55%">⋙生成源</text>
            <rect id="conver"></rect>
        </svg>
        <svg class="button">
            <text x="50%" y="55%">✗清空表单</text>
            <rect id="initial"></rect>
        </svg>
        <svg class="button">
            <text x="50%" y="55%">↶撤销操作</text>
            <rect id="undo"></rect>
        </svg>
        <svg class="button">
            <text x="50%" y="55%">↷重做操作</text>
            <rect id="redo"></rect>
        </svg>
        <svg class="button">
            <text x="50%" y="55%">⇏调试源</text>
            <rect id="debug"></rect>
        </svg>
        <svg class="button">
            <text x="50%" y="55%">✓保存源</text>
            <rect id="accept"></rect>
        </svg>
    </div>
    <div class="outbox">
        <div class="tabbox">
            <div class="tabtitle">
                <div name="编辑源" class="tab1 this">编辑源</div>
                <div name="调试源" class="tab2">调试源</div>
                <div name="源列表" class="tab3">源列表</div>
                <div name="帮助信息" class="tab4">帮助信息</div>
            </div>
            <div class="tabbody">
                <div class="tab1 this">
                        <textarea class="context" id="RuleJsonString"
                                  placeholder="这里输出序列化的JSON数据,可直接导入'阅读'APP"></textarea>
                </div>
                <div class="tab2">
                    <input type="text" class="inputbox" id="DebugKey" placeholder="输入搜索关键字，默认搜「我的」">
                    <textarea class="context" id="DebugConsole" placeholder="这里用于输出调试信息"></textarea>
                </div>
                <div class="tab3">
                    <input type="text" class="inputbox" id="Filter"
                           placeholder="输入筛选关键词（源名称、源URL或源分组）后按回车筛选源">
                    <div class="titlebar">
                        <button id="Import">导入源文件</button>
                        <button id="Export">导出源文件</button>
                        <button id="Delete">删除选中源</button>
                        <button id="ClrAll">清空列表</button>
                    </div>
                    <div class="context" id="RuleList"></div>
                </div>
                <div class="tab4">
                    <div class="context link">
                        <a target="_blank" href="https://alanskycn.gitee.io/teachme">源制作教程</a>
                        <a target="_blank"
                           href="https://zhuanlan.zhihu.com/p/29436838">Xpath基础教程</a>
                        <a target="_blank"
                           href="https://zhuanlan.zhihu.com/p/32187820">Xpath高级教程</a>
                        <a target="_blank" href="https://www.w3cschool.cn/regex_rmjc">正则表达式教程</a>
                        <a target="_blank" href="https://regexr.com">正则表达式在线验证工具</a>
                        <div>^$()[]{}.?+*| 这些是Java正则特殊符号,匹配需转义
                            <br>(?s) 前缀表示跨行解析
                            <br>(?m) 前缀表示逐行匹配
                            <br>(?i) 前缀表示忽略大小写
                        </div>
                        <a target="_blank" href="https://www.browxy.com/">代码在线运行工具</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="index.js"></script>
</body>

</html>