{"name": "reader", "version": "2.5.4", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "sync": "yarn build && rm -rf ../src/main/resources/web && mv dist ../src/main/resources/web"}, "dependencies": {"axios": "^0.21.1", "codejar": "^3.5.0", "core-js": "^3.3.2", "element-ui": "^2.15.9", "localforage": "^1.10.0", "prismjs": "^1.25.0", "register-service-worker": "^1.7.1", "sortablejs": "^1.15.0", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "vue": "^2.6.10", "vue-lazyload": "^1.3.3", "vue-router": "^3.1.3", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.0.0", "@vue/cli-plugin-eslint": "^4.0.0", "@vue/cli-plugin-pwa": "^4.0.0", "@vue/cli-plugin-router": "^4.0.0", "@vue/cli-service": "^4.0.0", "@vue/eslint-config-prettier": "^5.0.0", "babel-eslint": "^10.0.3", "babel-plugin-component": "^1.1.1", "eslint": "^5.16.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^5.0.0", "prettier": "^1.18.2", "vue-cli-plugin-element": "^1.0.1", "vue-template-compiler": "^2.6.10"}}