<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=0,minimal-ui,viewport-fit=cover" />
    <meta name="keyword" content="阅读3,在线版">
    <meta name="description" content="">
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="format-detection" content="telephone=no,email=no,address=no">
    <meta name="renderer" content="webkit">
    <meta name="HandheldFriendly" content="true">
    <meta name="MobileOptimized" content="320">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">
    <meta name="msapplication-tap-highlight" content="no">
    <link rel="icon" href="<%= BASE_URL %>logo2.png" />
    <title>书空信使</title>
    <script>
      // 通用加载脚本方法
      function loadScript(src, callback, async) {
        var script = document.createElement('script');
        if (async) script.async = 'async';
        script.src = src;

        if (callback) {
          script.onload = callback;
        }

        document.head.appendChild(script);
      }

      // 通用加载样式方法
      function loadLink(href, callback, rel, type) {
        var link = document.createElement('link');
        link.rel = rel || "stylesheet";
        link.type = type || "text/css";
        link.href = href;

        if (callback) {
          link.onload = callback;
        }

        document.getElementsByTagName("head")[0].appendChild(link);
      }

      // 获取单个查询参数
      function getQueryString(sVar, first) {
        return decodeURI(window.location.search.replace(new RegExp('^(?:.*' + (first ? '?' : '') + '[&\\?]' + encodeURI(sVar).replace(/[\.\+\*]/g, '\\$&') + '(?:\\=([^&]*))?)?.*$', 'i'), '$1'));
      }

      window.onerror = function() {
        if (window.getQueryString('debug')) {
          alert(JSON.stringify(arguments));
        }
      }

      if (window.getQueryString('debug')) {
        window.loadScript('https://cdn.bootcdn.net/ajax/libs/vConsole/3.9.1/vconsole.min.js', function() {
          new VConsole();
        }, true);
      }
    </script>
  </head>
  <style>
    :root {
      --sat: constant(safe-area-inset-top);
      --sat: env(safe-area-inset-top);
      --sar: constant(safe-area-inset-right);
      --sar: env(safe-area-inset-right);
      --sab: constant(safe-area-inset-bottom);
      --sab: env(safe-area-inset-bottom);
      --sal: constant(safe-area-inset-left);
      --sal: env(safe-area-inset-left);
    }
    body::-webkit-scrollbar {
      display: none;
    }
    body.night-theme::-webkit-scrollbar {
      background-color: #333 !important;
    }
    html,body {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
    }
  </style>
  <body>
    <noscript>
      <strong>请启用浏览器的Javascript支持</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
