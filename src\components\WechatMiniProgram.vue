<template>
  <el-dialog
    title="体验微信小程序【灵思测评】"
    :visible.sync="show"
    :width="dialogSmallWidth"
    :top="dialogTop"
    :fullscreen="$store.state.miniInterface"
    :class="
      isWebApp && !$store.getters.isNight ? 'status-bar-light-bg-dialog' : ''
    "
    :before-close="cancel"
  >
    <el-image
      :src="require('../assets/imgs/lingsi.png')"
      class="qrcode-img"
      fit="cover"
      lazy
    />
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  model: {
    prop: "show",
    event: "setShow"
  },
  name: "WechatMiniProgram",
  data() {
    return {};
  },
  props: ["show"],
  computed: {
    ...mapGetters(["dialogSmallWidth", "dialogTop"])
  },
  methods: {
    cancel() {
      this.$emit("setShow", false);
    }
  }
};
</script>
<style lang="stylus" scoped>
.qrcode-img {
  display: block;
  margin: 0 auto;
}
</style>
